# Quick Reference Guide
## CMS Task Management and Development Guide

### Task Execution Order
Follow this order for optimal development flow:

#### Critical Path (Must Complete First)
1. **001-005**: Foundation setup (PocketBase, environment, SSL, database, security)
2. **006-013**: All collection schemas (required for everything else)
3. **014-015**: Authentication and API setup
4. **026**: Content sync core framework (blocks all sync tasks)
5. **056**: GitHub Actions workflow (required for deployment)

#### Parallel Development Opportunities
After critical path completion, these can be developed in parallel:

**Team A - Content Migration**
- Tasks 016-025: Content analysis and migration scripts
- Tasks 027-030: API client and sync components

**Team B - Admin Interface**
- Tasks 036-045: Dashboard and content editor
- Tasks 046-050: Menu management system

**Team C - Hugo Integration**
- Tasks 031-035: Template updates and integration
- Tasks 033: Menu system integration (depends on 013, 046-050)

### High-Priority Tasks by Category

#### Foundation (Complete First)
- **001**: PocketBase Server Installation
- **002**: Environment Configuration  
- **006**: Site Settings Collection Schema
- **013**: Navigation Menus Collection Schema
- **015**: API Authentication Configuration

#### Core Functionality (Complete Second)
- **026**: Content Sync Core Framework
- **046**: Visual Menu Builder Interface
- **056**: GitHub Actions Workflow
- **031**: Hugo Template Refactoring

#### Advanced Features (Complete Last)
- **071**: Multilingual Content Schema
- **076**: Content Caching Strategy
- **081**: Technical Documentation

### Task Dependencies Quick Reference

#### Blocks Multiple Tasks
- **001** (PocketBase Installation) → Blocks all other tasks
- **004** (Database Initialization) → Blocks all schema tasks (006-013)
- **015** (API Authentication) → Blocks all sync tasks (026-035)
- **026** (Content Sync Framework) → Blocks sync components (027-030)
- **036** (Admin Dashboard) → Blocks UI tasks (037-055)

#### Critical Dependencies
- **013** (Navigation Schema) → **033** (Menu Integration), **046-050** (Menu UI)
- **012** (Media Library Schema) → **043** (Image Upload), **077** (CDN Integration)
- **026-035** (All Sync Tasks) → **056-060** (CI/CD Pipeline)

### Development Environment Setup

#### Required Tools
```bash
# Deno runtime
deno --version  # Should be 1.40+

# Docker (for containerized deployment)
docker --version  # Should be 20.10+
docker-compose --version  # Should be 2.0+

# Hugo
hugo version    # Should be 0.119.0+

# Git
git --version

# PocketBase binary
./pocketbase --version  # Should be 0.28.3+
```

#### Environment Variables
```bash
# Development
POCKETBASE_URL=http://localhost:8090
POCKETBASE_EMAIL=<EMAIL>
POCKETBASE_PASSWORD=your_password

# Production
POCKETBASE_URL=https://your-domain.com
NETLIFY_AUTH_TOKEN=your_token
NETLIFY_SITE_ID=your_site_id
```

### Common Commands

#### PocketBase Operations
```bash
# Start PocketBase server
./pocketbase serve

# Create admin user
./pocketbase <NAME_EMAIL> password123

# Import schema
./pocketbase admin import schema.json

# Backup database
./pocketbase backup create
```

#### Content Sync Operations (Deno)
```bash
# Run full sync
cd sync-framework && deno task sync

# Run incremental sync
cd sync-framework && deno run --allow-net --allow-read --allow-write --allow-env src/main.ts --incremental

# Sync specific collection
cd sync-framework && deno run --allow-net --allow-read --allow-write --allow-env src/main.ts --collection=pages

# Dry run (preview changes)
cd sync-framework && deno task sync:dry

# Development mode with file watching
cd sync-framework && deno task dev
```

#### Hugo Operations
```bash
# Local development server
hugo server -D

# Build for production
hugo --minify --environment production

# Build with specific config
hugo --config config.yaml,config-production.yaml
```

### Testing Commands

#### Unit Tests (Deno)
```bash
# Run all tests
cd sync-framework && deno task test

# Run specific test file
cd sync-framework && deno test tests/core/SyncManager.test.ts --allow-net --allow-read --allow-write --allow-env

# Run tests with coverage
cd sync-framework && deno test --coverage=coverage tests/

# Generate coverage report
cd sync-framework && deno coverage coverage --html
```

#### Integration Tests (Deno)
```bash
# Test PocketBase connection
cd sync-framework && deno test tests/integration/api.test.ts --allow-net --allow-env

# Test content sync
cd sync-framework && deno test tests/integration/sync.test.ts --allow-net --allow-read --allow-write --allow-env

# Test Hugo build integration
cd sync-framework && deno test tests/integration/hugo.test.ts --allow-read --allow-write
```

### Troubleshooting Quick Fixes

#### PocketBase Issues
```bash
# Check if PocketBase is running
curl http://localhost:8090/api/health

# Reset admin password
./pocketbase admin reset-password <EMAIL>

# Check logs
tail -f pb_data/logs/data.log
```

#### Sync Framework Issues (Deno)
```bash
# Clear sync cache
rm -rf sync-framework/.cache

# Verbose logging with Deno
cd sync-framework && deno run --allow-net --allow-read --allow-write --allow-env --log-level=debug src/main.ts

# Check API connectivity
cd sync-framework && deno test tests/connectivity.test.ts --allow-net --allow-env

# Check Deno permissions
cd sync-framework && deno info src/main.ts

# Format code
cd sync-framework && deno fmt

# Lint code
cd sync-framework && deno lint
```

#### Docker Operations
```bash
# Build development environment
cd sync-framework && docker-compose up --build

# Run in background
cd sync-framework && docker-compose up -d

# View sync service logs
cd sync-framework && docker-compose logs -f sync-service

# Stop all services
cd sync-framework && docker-compose down

# Build production image
cd sync-framework && docker build -t sync-service:latest .

# Run production container
docker run -d \
  --name sync-service \
  -e POCKETBASE_URL=https://your-pocketbase.com \
  -e POCKETBASE_EMAIL=<EMAIL> \
  -e POCKETBASE_PASSWORD=secure_password \
  -v $(pwd)/output:/app/output \
  sync-service:latest

# Check container status
docker ps
docker logs sync-service

# Execute commands in container
docker exec -it sync-service deno --version
```

#### Hugo Build Issues
```bash
# Clear Hugo cache
hugo mod clean

# Verbose build
hugo --verbose --debug

# Check for broken links
hugo --printI18nWarnings --printPathWarnings
```

### File Structure Reference

#### Project Root
```
ptblgh/
├── pocketbase/           # PocketBase server files
├── sync-framework/       # Content sync system
├── content/             # Hugo content files
├── themes/              # Hugo theme files
├── static/              # Static assets
├── config/              # Configuration files
├── scripts/             # Utility scripts
├── docs/                # Documentation
└── CMS_TASK/           # Task management files
```

#### Task Files
```
CMS_TASK/
├── task_index.md        # Master task list
├── project_summary.md   # Project overview
├── quick_reference.md   # This file
├── task_001.md         # Individual task files
├── task_002.md
└── ...
```

### Code Review Checklist

#### Before Submitting PR
- [ ] All tests pass
- [ ] Code follows project standards
- [ ] Documentation updated
- [ ] No sensitive data in code
- [ ] Error handling implemented
- [ ] Performance considerations addressed

#### Review Criteria
- [ ] Functionality works as specified
- [ ] Code is readable and maintainable
- [ ] Security best practices followed
- [ ] Performance impact acceptable
- [ ] Tests provide adequate coverage

### Emergency Procedures

#### Rollback Content Sync
```bash
# Stop automated sync
# Disable GitHub Actions workflow

# Restore from backup
./pocketbase backup restore backup-name.zip

# Revert Hugo content
git checkout HEAD~1 -- content/
```

#### Emergency Deployment
```bash
# Manual Hugo build and deploy
hugo --minify
netlify deploy --prod --dir=public
```

### Contact Information
- **Technical Issues**: Create GitHub issue with `bug` label
- **Task Questions**: Create GitHub issue with `question` label  
- **Urgent Issues**: Contact project lead directly
- **Documentation**: Update relevant task file and create PR

### Useful Links
- [PocketBase Documentation](https://pocketbase.io/docs/)
- [Hugo Documentation](https://gohugo.io/documentation/)
- [Project Repository](https://github.com/your-org/ptblgh)
- [Task Board](https://github.com/your-org/ptblgh/projects)

---
*Quick Reference Version: 1.0*
*Last Updated: [Current Date]*
