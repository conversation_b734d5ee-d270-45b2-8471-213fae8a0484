# Task 056: GitHub Actions Workflow

## Task Information
- **Task ID**: 056
- **Title**: GitHub Actions Workflow
- **Category**: CI/CD Pipeline
- **Phase**: 4
- **Priority**: High
- **Estimated Complexity**: Medium
- **Dependencies**: Tasks 026-035 (Content Sync and Hugo Integration)

## Description
Create a comprehensive GitHub Actions workflow that automates the content synchronization, Hugo build process, and deployment pipeline. The workflow should trigger on content changes in PocketBase, sync content, build the Hugo site, deploy the sync service to Railway, and deploy the static site to hosting platform.

## Detailed Requirements
1. Create automated content sync workflow
2. Implement Hugo build automation
3. Set up deployment to Railway for sync service
4. Configure static site deployment to hosting platform
5. Configure environment-specific workflows
6. Implement error handling and notifications
7. Add workflow monitoring and reporting
8. Create manual trigger options

## Technical Specifications
- **Platform**: GitHub Actions
- **Trigger Events**: Webhook, schedule, manual
- **Environments**: Development, Staging, Production
- **Build Tool**: Hugo
- **Sync Service Deployment**: Railway
- **Static Site Deployment**: GitHub Pages, Vercel, or similar
- **Notifications**: Slack, email, or GitHub

## Workflow Architecture
```yaml
# Main workflow structure
name: CMS Content Sync and Deploy

on:
  workflow_dispatch:  # Manual trigger
  schedule:           # Scheduled sync
    - cron: '*/15 * * * *'  # Every 15 minutes
  repository_dispatch: # PocketBase webhook
    types: [content-updated]

jobs:
  sync-content:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
      - name: Setup Node.js
      - name: Install dependencies
      - name: Sync content from PocketBase
      - name: Commit changes
      
  build-hugo:
    needs: sync-content
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
      - name: Setup Hugo
      - name: Build site
      - name: Upload artifacts
      
  deploy:
    needs: build-hugo
    runs-on: ubuntu-latest
    steps:
      - name: Download artifacts
      - name: Deploy to hosting
      - name: Notify completion
```

## Acceptance Criteria
- [ ] GitHub Actions workflow created
- [ ] Content sync automation working
- [ ] Hugo build process automated
- [ ] Railway deployment for sync service functional
- [ ] Static site deployment pipeline working
- [ ] Environment-specific configurations
- [ ] Error handling and notifications
- [ ] Manual trigger options available
- [ ] Workflow monitoring implemented

## Implementation Steps
1. **Main Workflow File**
   ```yaml
   # .github/workflows/cms-sync-deploy.yml
   name: CMS Content Sync and Deploy
   
   on:
     workflow_dispatch:
       inputs:
         environment:
           description: 'Deployment environment'
           required: true
           default: 'staging'
           type: choice
           options:
             - staging
             - production
     
     schedule:
       - cron: '*/15 * * * *'  # Every 15 minutes
     
     repository_dispatch:
       types: [content-updated]
   
   env:
     NODE_VERSION: '18'
     HUGO_VERSION: '0.119.0'
   ```

2. **Content Sync Job**
   ```yaml
   jobs:
     sync-content:
       runs-on: ubuntu-latest
       outputs:
         changes-detected: ${{ steps.sync.outputs.changes }}
       
       steps:
         - name: Checkout repository
           uses: actions/checkout@v4
           with:
             token: ${{ secrets.GITHUB_TOKEN }}
             fetch-depth: 0
         
         - name: Setup Node.js
           uses: actions/setup-node@v4
           with:
             node-version: ${{ env.NODE_VERSION }}
             cache: 'npm'
         
         - name: Install dependencies
           run: |
             cd sync-framework
             npm ci
         
         - name: Sync content from PocketBase
           id: sync
           run: |
             cd sync-framework
             npm run sync
           env:
             POCKETBASE_URL: ${{ secrets.POCKETBASE_URL }}
             POCKETBASE_EMAIL: ${{ secrets.POCKETBASE_EMAIL }}
             POCKETBASE_PASSWORD: ${{ secrets.POCKETBASE_PASSWORD }}
         
         - name: Commit and push changes
           if: steps.sync.outputs.changes == 'true'
           run: |
             git config --local user.email "<EMAIL>"
             git config --local user.name "GitHub Action"
             git add .
             git commit -m "Auto-sync content from PocketBase [skip ci]" || exit 0
             git push
   ```

3. **Hugo Build Job**
   ```yaml
     build-hugo:
       needs: sync-content
       if: needs.sync-content.outputs.changes-detected == 'true' || github.event_name == 'workflow_dispatch'
       runs-on: ubuntu-latest
       
       steps:
         - name: Checkout repository
           uses: actions/checkout@v4
           with:
             ref: ${{ github.ref }}
         
         - name: Setup Hugo
           uses: peaceiris/actions-hugo@v2
           with:
             hugo-version: ${{ env.HUGO_VERSION }}
             extended: true
         
         - name: Build Hugo site
           run: |
             hugo --minify --environment production
         
         - name: Upload build artifacts
           uses: actions/upload-artifact@v4
           with:
             name: hugo-site
             path: public/
             retention-days: 30
   ```

4. **Railway Sync Service Deployment**
   ```yaml
     deploy-sync-service:
       needs: sync-content
       if: success() && (github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch')
       runs-on: ubuntu-latest
       environment: ${{ github.event.inputs.environment || 'production' }}

       steps:
         - name: Checkout repository
           uses: actions/checkout@v4

         - name: Deploy to Railway
           uses: railwayapp/railway-deploy@v1.1.0
           with:
             railway_token: ${{ secrets.RAILWAY_TOKEN }}
             service: sync-service
             dockerfile_path: sync-framework/Dockerfile

   deploy-static-site:
     needs: [sync-content, build-hugo]
     if: success() && (github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch')
     runs-on: ubuntu-latest
     environment: ${{ github.event.inputs.environment || 'production' }}

     steps:
       - name: Download build artifacts
         uses: actions/download-artifact@v4
         with:
           name: hugo-site
           path: public/

       - name: Deploy to GitHub Pages
         uses: peaceiris/actions-gh-pages@v3
         if: github.ref == 'refs/heads/main'
         with:
           github_token: ${{ secrets.GITHUB_TOKEN }}
           publish_dir: ./public
           cname: your-domain.com  # Optional: custom domain

       # Alternative: Deploy to Vercel
       - name: Deploy to Vercel
         uses: amondnet/vercel-action@v25
         if: github.event.inputs.deployment_target == 'vercel'
         with:
           vercel-token: ${{ secrets.VERCEL_TOKEN }}
           vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
           vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
           working-directory: ./public
   ```

## Files to Create/Modify
- `.github/workflows/cms-sync-deploy.yml`
- `.github/workflows/content-sync-only.yml`
- `.github/workflows/manual-deploy.yml`
- `scripts/setup-github-secrets.sh`
- `docs/github-actions-setup.md`

## Environment Configuration
### Required Secrets
```bash
# PocketBase Configuration
POCKETBASE_URL=https://your-pocketbase-instance.com
POCKETBASE_EMAIL=<EMAIL>
POCKETBASE_PASSWORD=secure_password

# Railway Configuration
RAILWAY_TOKEN=your_railway_token

# Static Site Deployment (choose one)
# GitHub Pages (uses GITHUB_TOKEN automatically)

# Vercel (optional)
VERCEL_TOKEN=your_vercel_token
VERCEL_ORG_ID=your_org_id
VERCEL_PROJECT_ID=your_project_id

# Notification Configuration
SLACK_WEBHOOK_URL=your_slack_webhook
NOTIFICATION_EMAIL=<EMAIL>
```

### Environment Variables
```yaml
env:
  NODE_VERSION: '18'
  HUGO_VERSION: '0.119.0'
  SYNC_TIMEOUT: '300'
  BUILD_TIMEOUT: '600'
```

## Railway Configuration

### railway.json
```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "DOCKERFILE",
    "dockerfilePath": "sync-framework/Dockerfile"
  },
  "deploy": {
    "startCommand": "deno run --allow-net --allow-read --allow-write --allow-env src/main.ts",
    "healthcheckPath": "/health",
    "healthcheckTimeout": 100,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### Railway Environment Variables
```bash
# Set in Railway dashboard or via CLI
railway variables set POCKETBASE_URL=https://your-pocketbase.com
railway variables set POCKETBASE_EMAIL=<EMAIL>
railway variables set POCKETBASE_PASSWORD=secure_password
railway variables set SYNC_INTERVAL=300
railway variables set LOG_LEVEL=info
```

## Workflow Variations

### 1. Content-Only Sync
```yaml
# For frequent content updates without full rebuild
name: Content Sync Only
on:
  schedule:
    - cron: '*/5 * * * *'  # Every 5 minutes

jobs:
  sync-only:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Railway sync service
        run: |
          curl -X POST "${{ secrets.RAILWAY_WEBHOOK_URL }}" \
            -H "Authorization: Bearer ${{ secrets.RAILWAY_TOKEN }}" \
            -d '{"action": "sync"}'
```

### 2. Manual Deployment with Options
```yaml
# For manual deployments with options
name: Manual Deploy
on:
  workflow_dispatch:
    inputs:
      skip_sync:
        description: 'Skip content sync'
        type: boolean
        default: false
      deployment_target:
        description: 'Deployment target'
        type: choice
        options:
          - github-pages
          - vercel
        default: github-pages
      railway_service:
        description: 'Deploy sync service to Railway'
        type: boolean
        default: true
```

### 3. Emergency Rollback
```yaml
# For quick rollbacks
name: Emergency Rollback
on:
  workflow_dispatch:
    inputs:
      commit_sha:
        description: 'Commit SHA to rollback to'
        required: true
      rollback_railway:
        description: 'Rollback Railway service'
        type: boolean
        default: false

jobs:
  rollback:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout specific commit
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.commit_sha }}

      - name: Rollback Railway service
        if: github.event.inputs.rollback_railway == 'true'
        uses: railwayapp/railway-deploy@v1.1.0
        with:
          railway_token: ${{ secrets.RAILWAY_TOKEN }}
          service: sync-service
```

## Monitoring and Notifications

```yaml
- name: Notify on failure
  if: failure()
  uses: 8398a7/action-slack@v3
  with:
    status: failure
    text: |
      🚨 CMS Deployment Failed!
      - Sync Service: ${{ steps.deploy-sync.outcome }}
      - Static Site: ${{ steps.deploy-static.outcome }}
      - Commit: ${{ github.sha }}
  env:
    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

- name: Notify on success
  if: success()
  uses: 8398a7/action-slack@v3
  with:
    status: success
    text: |
      ✅ CMS Deployment Successful!
      - Railway Service: Deployed
      - Static Site: Updated
      - Commit: ${{ github.sha }}
      - Railway URL: ${{ steps.railway-deploy.outputs.url }}
  env:
    SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

- name: Railway deployment status
  if: always()
  run: |
    echo "Railway deployment status: ${{ steps.railway-deploy.outcome }}"
    echo "Railway service URL: ${{ steps.railway-deploy.outputs.url }}"
```

## Testing Checklist
- [ ] Workflow triggers correctly
- [ ] Content sync executes successfully
- [ ] Hugo build completes without errors
- [ ] Railway deployment succeeds
- [ ] Static site deployment works
- [ ] Error handling works properly
- [ ] Notifications are sent
- [ ] Manual triggers function
- [ ] Environment variables accessible
- [ ] Railway secrets properly configured
- [ ] Health checks pass on Railway service

## Performance Optimization
- Cache Node.js dependencies
- Cache Hugo resources
- Parallel job execution where possible
- Conditional job execution
- Artifact cleanup automation

## Security Considerations
- Secure secret management
- Limited workflow permissions
- Environment protection rules
- Audit logging enabled
- Access control for manual triggers

## Rollback Plan
- Disable automated workflows
- Revert Railway service to previous deployment
- Restore previous static site deployment
- Use Railway CLI for emergency rollbacks
- Document any issues encountered

### Railway Rollback Commands
```bash
# Rollback Railway service to previous deployment
railway rollback --service sync-service

# Check deployment history
railway logs --service sync-service

# Manual redeploy from specific commit
railway deploy --service sync-service --detach
```

## Notes
- Monitor workflow execution times
- Optimize for cost efficiency with Railway's usage-based pricing
- Plan for scaling requirements with Railway's auto-scaling
- Consider workflow complexity and Railway service dependencies
- Railway provides built-in monitoring and logging
- Use Railway's preview deployments for testing
- Consider Railway's database services for future expansion

## Progress Tracker
- [ ] **Planning**: Requirements analysis and approach definition
- [ ] **Setup**: Environment preparation and tool installation
- [ ] **Implementation**: Core functionality development
- [ ] **Testing**: Verification and validation
- [ ] **Documentation**: User guides and technical docs
- [ ] **Review**: Code review and quality assurance
- [ ] **Deployment**: Production deployment preparation
- [ ] **Complete**: Task fully implemented and verified

## Related Tasks
- **Previous**: Task 055 (User Session Management)
- **Next**: Task 057 (Railway Integration Setup)
- **Related**: Task 026-035 (Content Sync), Task 059 (Build Hook Implementation)

## Risk Assessment
- **Medium Risk**: CI/CD complexity and dependencies
- **Mitigation**: Incremental implementation, comprehensive testing, fallback procedures

---
*Task Created: [Current Date]*
*Last Updated: [Current Date]*
*Status: Not Started*
